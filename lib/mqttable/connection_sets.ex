defmodule Mqttable.ConnectionSets do
  @moduledoc """
  API module for managing connection sets and UI state.
  Provides functions to get, update, and save connection sets and UI state.
  Also provides subscription functionality for real-time updates.
  """

  alias Mqttable.ConnectionSets.Server
  alias Phoenix.PubSub

  @pubsub_topic "brokers"

  @doc """
  Gets all connection sets.

  ## Returns

  - List of connection sets
  """
  def get_all do
    Server.get_all()
  end

  @doc """
  Updates the connection sets.

  ## Parameters

  - `connection_sets`: List of connection set maps to be updated

  ## Returns

  - `:ok`: Always returns `:ok`
  """
  def update(connection_sets) when is_list(connection_sets) do
    Server.update(connection_sets)
    :ok
  end

  @doc """
  Forces an immediate save of connection sets to disk.

  NOTE: This should only be used for critical operations like deletion.
  For normal updates, rely on the periodic save mechanism.

  ## Returns

  - `:ok`: Always returns `:ok`
  """
  def save_to_disk do
    Server.save_to_disk()
    :ok
  end

  @doc """
  Loads connection sets from storage.
  This is a compatibility function that delegates to get_all.

  ## Returns

  - `{:ok, connection_sets}`: Returns the list of connection sets
  """
  def load do
    {:ok, get_all()}
  end

  @doc """
  Saves connection sets to storage.
  This is a compatibility function that delegates to update.

  ## Parameters

  - `connection_sets`: List of connection set maps to be saved

  ## Returns

  - `{:ok, "memory"}`: Indicates the connection sets were updated in memory
  """
  def save(connection_sets) when is_list(connection_sets) do
    update(connection_sets)
    {:ok, "memory"}
  end

  @doc """
  Subscribe to connection sets updates.
  The subscriber will receive a message in the format:
  {:connection_sets_updated, connection_sets}

  ## Returns

  - `:ok`: Always returns `:ok`
  """
  def subscribe do
    PubSub.subscribe(Mqttable.PubSub, @pubsub_topic)
  end

  @doc """
  Unsubscribe from connection sets updates.

  ## Returns

  - `:ok`: Always returns `:ok`
  """
  def unsubscribe do
    PubSub.unsubscribe(Mqttable.PubSub, @pubsub_topic)
  end

  @doc """
  Gets the UI state.

  ## Returns

  - Map containing UI state information
  """
  def get_ui_state do
    Server.get_ui_state()
  end

  @doc """
  Updates the UI state.

  ## Parameters

  - `ui_state`: Map containing UI state information to be updated

  ## Returns

  - `:ok`: Always returns `:ok`
  """
  def update_ui_state(ui_state) when is_map(ui_state) do
    Server.update_ui_state(ui_state)
    :ok
  end

  @doc """
  Updates the expanded sets state in the UI state.

  ## Parameters

  - `expanded_sets`: Map of set names to boolean values indicating expansion state

  ## Returns

  - `:ok`: Always returns `:ok`
  """
  def update_expanded_sets(expanded_sets) when is_map(expanded_sets) do
    # Ensure all keys are strings for consistency
    normalized_expanded_sets =
      expanded_sets
      |> Enum.map(fn {key, value} -> {to_string(key), value} end)
      |> Enum.into(%{})

    ui_state = get_ui_state()
    updated_ui_state = Map.put(ui_state, :expanded_sets, normalized_expanded_sets)
    update_ui_state(updated_ui_state)
  end

  @doc """
  Synchronizes the expanded sets state with the current connection sets.
  Removes any expanded set entries that no longer exist in the connection sets.

  ## Parameters

  - `connection_sets`: List of connection set maps

  ## Returns

  - `:ok`: Always returns `:ok`
  """
  def sync_expanded_sets(connection_sets) when is_list(connection_sets) do
    # Get current UI state
    ui_state = get_ui_state()
    expanded_sets = Map.get(ui_state, :expanded_sets, %{})

    # Get all valid connection set names as strings
    valid_names = Enum.map(connection_sets, fn set -> to_string(Map.get(set, :name)) end)

    # Filter out expanded sets that no longer exist
    # Only keep keys that correspond to existing brokers
    updated_expanded_sets =
      expanded_sets
      |> Enum.map(fn {key, value} -> {to_string(key), value} end)
      |> Enum.filter(fn {key, _value} -> key in valid_names end)
      |> Enum.into(%{})

    # Update the UI state
    updated_ui_state = Map.put(ui_state, :expanded_sets, updated_expanded_sets)
    update_ui_state(updated_ui_state)
  end

  @doc """
  Updates the active connection set in the UI state.

  ## Parameters

  - `active_set_name`: String name of the active connection set

  ## Returns

  - `:ok`: Always returns `:ok`
  """
  def update_active_connection_set(active_set_name)
      when is_binary(active_set_name) or is_nil(active_set_name) do
    # Get current UI state
    ui_state = get_ui_state()

    # Update the UI state with the active connection set name
    updated_ui_state = Map.put(ui_state, :active_connection_set, active_set_name)
    update_ui_state(updated_ui_state)
  end

  @doc """
  Updates the send modal form state for a specific broker in the UI state.

  ## Parameters

  - `broker_name`: String name of the broker
  - `form_state`: Map containing the form state

  ## Returns

  - `:ok`: Always returns `:ok`
  """
  def update_send_modal_form_state(broker_name, form_state)
      when is_binary(broker_name) and is_map(form_state) do
    # Get current UI state
    ui_state = get_ui_state()
    send_modal_forms = Map.get(ui_state, :send_modal_forms, %{})

    # Update the form state for this broker
    updated_send_modal_forms = Map.put(send_modal_forms, broker_name, form_state)
    updated_ui_state = Map.put(ui_state, :send_modal_forms, updated_send_modal_forms)
    update_ui_state(updated_ui_state)
  end

  @doc """
  Gets the send modal form state for a specific broker from the UI state.

  ## Parameters

  - `broker_name`: String name of the broker

  ## Returns

  - `form_state`: Map containing the form state, or nil if not found
  """
  def get_send_modal_form_state(broker_name) when is_binary(broker_name) do
    ui_state = get_ui_state()
    send_modal_forms = Map.get(ui_state, :send_modal_forms, %{})
    Map.get(send_modal_forms, broker_name)
  end

  @doc """
  Cleans all UI state data related to a specific broker.
  This includes expanded_sets, send_modal_forms, trace_filters, and mqtt5_properties_collapsed.

  ## Parameters

  - `broker_name`: String name of the broker to clean from UI state

  ## Returns

  - `:ok`: Always returns `:ok`
  """
  def clean_broker_ui_state(broker_name) when is_binary(broker_name) do
    # Get current UI state
    ui_state = get_ui_state()

    # Clean expanded_sets
    expanded_sets = Map.get(ui_state, :expanded_sets, %{})
    updated_expanded_sets = Map.delete(expanded_sets, broker_name)

    # Clean send_modal_forms
    send_modal_forms = Map.get(ui_state, :send_modal_forms, %{})
    updated_send_modal_forms = Map.delete(send_modal_forms, broker_name)

    # Clean trace_filters
    trace_filters = Map.get(ui_state, :trace_filters, %{})
    updated_trace_filters = Map.delete(trace_filters, broker_name)

    # Clean mqtt5_properties_collapsed
    mqtt5_collapsed = Map.get(ui_state, :mqtt5_properties_collapsed, %{})
    updated_mqtt5_collapsed = Map.delete(mqtt5_collapsed, broker_name)

    # Update the UI state with cleaned data
    updated_ui_state =
      ui_state
      |> Map.put(:expanded_sets, updated_expanded_sets)
      |> Map.put(:send_modal_forms, updated_send_modal_forms)
      |> Map.put(:trace_filters, updated_trace_filters)
      |> Map.put(:mqtt5_properties_collapsed, updated_mqtt5_collapsed)

    update_ui_state(updated_ui_state)
  end

  @doc """
  Resets all connection sets and related data to factory defaults.
  This includes:
  - Disconnecting all MQTT connections
  - Clearing all trace messages
  - Removing all connection sets
  - Resetting UI state to defaults
  - Forcing immediate save to disk

  ## Returns

  - `:ok`: Always returns `:ok`
  """
  def reset_all_data do
    # Get all current connection sets to find active connections
    connection_sets = get_all()

    # Disconnect all MQTT connections
    Enum.each(connection_sets, fn connection_set ->
      connections = Map.get(connection_set, :connections, [])

      Enum.each(connections, fn connection ->
        if connection.client_id do
          Mqttable.MqttClient.Manager.disconnect(connection.client_id)
        end
      end)
    end)

    # Clear all trace messages for all brokers
    Enum.each(connection_sets, fn connection_set ->
      broker_name = Map.get(connection_set, :name)

      if broker_name do
        Mqttable.TraceManager.remove_broker(broker_name)
      end
    end)

    # Reset connection sets to empty list
    update([])

    # Reset UI state to defaults
    default_ui_state = %{expanded_sets: %{}}
    update_ui_state(default_ui_state)

    # Force immediate save to disk
    save_to_disk()

    :ok
  end
end
