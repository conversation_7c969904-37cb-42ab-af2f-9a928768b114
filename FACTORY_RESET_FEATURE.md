# Factory Reset Feature

## 概述

在设置界面添加了恢复出厂设置功能，需要2次确认并清空所有broker数据。

## 功能特性

### 1. 恢复出厂设置操作包括：
- 断开所有MQTT连接
- 清空所有trace消息
- 删除所有broker配置
- 重置UI状态为默认值
- 重置应用设置为默认值
- 立即保存到磁盘

### 2. 双重确认机制：
- **第一次确认**：显示警告对话框，列出将要删除的内容
- **第二次确认**：最终确认对话框，要求用户再次确认

### 3. UI设计：
- 在设置模态框中添加了"危险区域"（Danger Zone）
- 使用DaisyUI的错误样式突出显示危险性
- 清晰的警告文本和图标

## 代码实现

### 1. 后端函数

#### `Mqttable.Settings.reset_to_factory_defaults/0`
```elixir
def reset_to_factory_defaults do
  default_settings = Storage.default_settings()
  Server.update(default_settings)
  :ok
end
```

#### `Mqttable.ConnectionSets.reset_all_data/0`
```elixir
def reset_all_data do
  # 获取所有当前连接集合
  connection_sets = get_all()

  # 断开所有MQTT连接
  Enum.each(connection_sets, fn connection_set ->
    connections = Map.get(connection_set, :connections, [])
    Enum.each(connections, fn connection ->
      if connection.client_id do
        Mqttable.MqttClient.Manager.disconnect(connection.client_id)
      end
    end)
  end)

  # 清空所有trace消息
  Enum.each(connection_sets, fn connection_set ->
    broker_name = Map.get(connection_set, :name)
    if broker_name do
      Mqttable.TraceManager.remove_broker(broker_name)
    end
  end)

  # 重置连接集合为空列表
  update([])

  # 重置UI状态为默认值
  default_ui_state = %{expanded_sets: %{}}
  update_ui_state(default_ui_state)

  # 强制立即保存到磁盘
  save_to_disk()

  :ok
end
```

### 2. 前端组件

#### 设置模态框组件更新
- 添加了`factory_reset_step`状态管理
- 新增危险区域UI
- 实现了两个确认对话框
- 添加了相关事件处理函数

#### 事件处理函数：
- `start_factory_reset` - 开始恢复出厂设置流程
- `cancel_factory_reset` - 取消恢复出厂设置
- `confirm_factory_reset_first` - 第一次确认
- `execute_factory_reset` - 执行恢复出厂设置

## 使用方法

1. 打开应用设置界面
2. 滚动到底部的"危险区域"
3. 点击"Factory Reset"按钮
4. 在第一个确认对话框中点击"Continue"
5. 在最终确认对话框中点击"Yes, Reset Everything"

## 安全考虑

- 双重确认机制防止误操作
- 清晰的警告信息说明操作后果
- 使用错误样式突出显示危险性
- 操作不可逆转的明确提示

## 文件修改

1. `lib/mqttable/settings.ex` - 添加恢复出厂设置函数
2. `lib/mqttable/connection_sets.ex` - 添加清空所有数据函数
3. `lib/mqttable_web/live/components/settings_modal_component.ex` - 更新UI和事件处理

## 测试

可以运行 `demo_factory_reset.exs` 脚本查看当前系统状态和恢复出厂设置功能的演示。

```bash
elixir demo_factory_reset.exs
```

## 注意事项

- 恢复出厂设置操作是不可逆的
- 执行前请确保已备份重要配置
- 操作完成后页面会自动刷新
